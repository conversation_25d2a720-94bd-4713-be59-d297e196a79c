"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { Offer, OfferType } from "@/types/models";
import { getOffers, deleteOffer } from "@/lib/firebase/firestore";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import OfferModal from "@/components/admin-dashboard/offers/OfferModal";
import { toast } from "@/hooks/use-toast";

export default function OffersPage() {
  const { t, isClient, locale } = useLocale();
  const { user } = useAuth();
  const isRTL = locale === 'ar';

  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [isOfferModalOpen, setIsOfferModalOpen] = useState(false);
  const [editingOffer, setEditingOffer] = useState<Offer | null>(null);

  // Fetch offers
  const fetchOffers = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const fetchedOffers = await getOffers(user.uid);
      setOffers(fetchedOffers);
    } catch (error) {
      console.error('Error fetching offers:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch offers. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOffers();
  }, [user]);

  // Filter offers based on search and tab
  const filteredOffers = offers.filter(offer => {
    const matchesSearch = offer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         offer.description.toLowerCase().includes(searchQuery.toLowerCase());

    switch (activeTab) {
      case 'active':
        return matchesSearch && offer.isActive && new Date(offer.endDate) >= new Date();
      case 'expired':
        return matchesSearch && new Date(offer.endDate) < new Date();
      case 'inactive':
        return matchesSearch && !offer.isActive;
      case 'packages':
        return matchesSearch && offer.type === OfferType.PACKAGE_DEAL;
      default:
        return matchesSearch;
    }
  });

  // Handle offer deletion
  const handleDeleteOffer = async (offerId: string) => {
    if (!confirm('Are you sure you want to delete this offer?')) return;

    try {
      await deleteOffer(offerId);
      setOffers(prev => prev.filter(offer => offer.id !== offerId));
      toast({
        title: 'Success',
        description: 'Offer deleted successfully.',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error deleting offer:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete offer. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle offer creation/update
  const handleOfferSaved = (savedOffer: Offer) => {
    if (editingOffer) {
      setOffers(prev => prev.map(offer =>
        offer.id === savedOffer.id ? savedOffer : offer
      ));
    } else {
      setOffers(prev => [savedOffer, ...prev]);
    }
    setIsOfferModalOpen(false);
    setEditingOffer(null);
  };

  // Get offer type badge color
  const getOfferTypeBadge = (type: OfferType) => {
    switch (type) {
      case OfferType.PERCENTAGE:
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">%</Badge>;
      case OfferType.FIXED_AMOUNT:
        return <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">SAR</Badge>;
      case OfferType.FREE_DELIVERY:
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">🚚</Badge>;
      case OfferType.PACKAGE_DEAL:
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">📦</Badge>;
      default:
        return <Badge variant="secondary">🏷️</Badge>;
    }
  };

  return (
    <div className="space-y-6" dir={isRTL ? "rtl" : "ltr"}>
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
            {isClient ? t('admin.offers.offersTitle') : 'Offers & Discounts'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {isClient ? t('admin.offers.offersDescription') : 'Manage and create offers and discounts for your menu items'}
          </p>
        </div>
        <Button
          onClick={() => setIsOfferModalOpen(true)}
          className="bg-[#56999B] hover:bg-[#56999B]/90 dark:bg-[#5DBDC0] dark:hover:bg-[#5DBDC0]/90"
        >
          <i className={`fa-solid fa-plus ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
          {isClient ? t('admin.offers.createOffer') : 'Create Offer'}
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder={isClient ? t('admin.offers.searchOffers') : 'Search offers...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-white dark:bg-[#242832] border-gray-300 dark:border-gray-600"
            />
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-gray-100 dark:bg-[#242832]">
            <TabsTrigger value="all" className="data-[state=active]:bg-white dark:data-[state=active]:bg-[#1d2127]">
              {isClient ? t('admin.offers.tabs.all') : 'All'}
            </TabsTrigger>
            <TabsTrigger value="active" className="data-[state=active]:bg-white dark:data-[state=active]:bg-[#1d2127]">
              {isClient ? t('admin.offers.tabs.active') : 'Active'}
            </TabsTrigger>
            <TabsTrigger value="expired" className="data-[state=active]:bg-white dark:data-[state=active]:bg-[#1d2127]">
              {isClient ? t('admin.offers.tabs.expired') : 'Expired'}
            </TabsTrigger>
            <TabsTrigger value="inactive" className="data-[state=active]:bg-white dark:data-[state=active]:bg-[#1d2127]">
              {isClient ? t('admin.offers.tabs.inactive') : 'Inactive'}
            </TabsTrigger>
            <TabsTrigger value="packages" className="data-[state=active]:bg-white dark:data-[state=active]:bg-[#1d2127]">
              {isClient ? t('admin.offers.tabs.packages') : 'Packages'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#56999B] dark:border-[#5DBDC0]"></div>
              </div>
            ) : filteredOffers.length === 0 ? (
              <div className="text-center py-12">
                <i className="fa-solid fa-tag text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery ?
                    (isClient ? t('admin.offers.noSearchResults') : 'No offers found matching your search.') :
                    (isClient ? t('admin.offers.noOffers') : 'No offers created yet. Create your first offer!')
                  }
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredOffers.map((offer) => (
                  <div key={offer.id} className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    {/* Offer Header */}
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center gap-2">
                        {getOfferTypeBadge(offer.type)}
                        <Badge variant={offer.isActive ? "default" : "secondary"}>
                          {offer.isActive ?
                            (isClient ? t('admin.offers.status.active') : 'Active') :
                            (isClient ? t('admin.offers.status.inactive') : 'Inactive')
                          }
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingOffer(offer);
                            setIsOfferModalOpen(true);
                          }}
                        >
                          <i className="fa-solid fa-edit text-sm"></i>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteOffer(offer.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        >
                          <i className="fa-solid fa-trash text-sm"></i>
                        </Button>
                      </div>
                    </div>

                    {/* Offer Content */}
                    <div className="space-y-3">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                        {locale === 'ar' && offer.name_ar ? offer.name_ar : offer.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                        {locale === 'ar' && offer.description_ar ? offer.description_ar : offer.description}
                      </p>

                      {/* Offer Details */}
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500 dark:text-gray-400">
                            {isClient ? t('admin.offers.discount') : 'Discount'}:
                          </span>
                          <span className="font-medium">
                            {offer.type === OfferType.PERCENTAGE ?
                              `${offer.discountValue}%` :
                              offer.type === OfferType.FIXED_AMOUNT ?
                              `SAR ${offer.discountValue}` :
                              offer.type === OfferType.FREE_DELIVERY ?
                              (isClient ? t('admin.offers.freeDelivery') : 'Free Delivery') :
                              (isClient ? t('admin.offers.packageDeal') : 'Package Deal')
                            }
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500 dark:text-gray-400">
                            {isClient ? t('admin.offers.usage') : 'Usage'}:
                          </span>
                          <span className="font-medium">
                            {offer.usageCount}{offer.usageLimit ? `/${offer.usageLimit}` : ''}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500 dark:text-gray-400">
                            {isClient ? t('admin.offers.expires') : 'Expires'}:
                          </span>
                          <span className="font-medium">
                            {new Date(offer.endDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Offer Modal */}
      <OfferModal
        isOpen={isOfferModalOpen}
        onClose={() => {
          setIsOfferModalOpen(false);
          setEditingOffer(null);
        }}
        onOfferSaved={handleOfferSaved}
        editingOffer={editingOffer}
      />
    </div>
  );
}